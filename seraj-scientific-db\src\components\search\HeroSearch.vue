<template>
  <section class="relative bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 py-16 lg:py-24">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000000" fill-opacity="0.1"%3E%3Ccircle cx="7" cy="7" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
          <span class="bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent">
            جستجو در میان
          </span>
          <br />
          <span class="text-gray-900 dark:text-white">هزاران مقاله علمی</span>
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
          دسترسی آسان به بزرگ‌ترین مجموعه مقالات و پژوهش‌های علمی به زبان فارسی. 
          جستجو کنید، کشف کنید، و دانش خود را گسترش دهید.
        </p>
      </div>

      <!-- Main Search Form -->
      <div class="max-w-4xl mx-auto">
        <form @submit.prevent="performSearch" class="relative">
          <div class="relative">
            <!-- Search Input -->
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="عنوان مقاله، نویسنده، کلیدواژه یا موضوع مورد نظر خود را جستجو کنید..."
                class="search-neu w-full pr-16 rtl:pr-6 rtl:pl-16 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400"
                :class="{ 'rounded-b-none': showSuggestions && suggestions.length > 0 }"
                @input="handleSearchInput"
                @focus="showSuggestions = true"
                @keydown="handleKeydown"
                ref="searchInput"
                autocomplete="off"
              />
              
              <!-- Search Icon -->
              <div class="absolute right-4 rtl:right-auto rtl:left-4 top-1/2 transform -translate-y-1/2">
                <MagnifyingGlassIcon class="w-6 h-6 text-gray-400" />
              </div>

              <!-- Clear Button -->
              <button
                v-if="searchQuery"
                @click="clearSearch"
                type="button"
                class="absolute left-4 rtl:left-auto rtl:right-4 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
                title="پاک کردن"
              >
                <XMarkIcon class="w-5 h-5 text-gray-400" />
              </button>
            </div>

            <!-- Search Suggestions -->
            <Transition
              enter-active-class="transition duration-200 ease-out"
              enter-from-class="transform scale-95 opacity-0"
              enter-to-class="transform scale-100 opacity-100"
              leave-active-class="transition duration-150 ease-in"
              leave-from-class="transform scale-100 opacity-100"
              leave-to-class="transform scale-95 opacity-0"
            >
              <div
                v-if="showSuggestions && suggestions.length > 0"
                class="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 rounded-b-neu-xl shadow-neu-lg dark:shadow-neu-dark-lg border-t border-gray-200 dark:border-gray-700 max-h-80 overflow-y-auto z-10"
              >
                <ul class="py-2">
                  <li
                    v-for="(suggestion, index) in suggestions"
                    :key="index"
                    @click="selectSuggestion(suggestion)"
                    :class="[
                      'px-6 py-3 cursor-pointer transition-colors duration-150',
                      {
                        'bg-primary-50 dark:bg-primary-900/20': selectedSuggestionIndex === index,
                        'hover:bg-gray-50 dark:hover:bg-gray-700': selectedSuggestionIndex !== index
                      }
                    ]"
                  >
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                      <MagnifyingGlassIcon class="w-4 h-4 text-gray-400 flex-shrink-0" />
                      <span class="text-gray-900 dark:text-white">{{ suggestion.text }}</span>
                      <span v-if="suggestion.count" class="text-sm text-gray-500 dark:text-gray-400">
                        ({{ suggestion.count }} نتیجه)
                      </span>
                    </div>
                  </li>
                </ul>
              </div>
            </Transition>
          </div>

          <!-- Search Actions -->
          <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse mt-6">
            <button
              type="submit"
              class="btn-neu-primary text-lg px-8 py-4 min-w-[200px] focus-neu"
              :disabled="!searchQuery.trim()"
            >
              <MagnifyingGlassIcon class="w-5 h-5 inline ml-2 rtl:ml-0 rtl:mr-2" />
              جستجو
            </button>
            
            <button
              @click="toggleAdvancedSearch"
              type="button"
              class="btn-neu-secondary text-lg px-6 py-4 focus-neu"
            >
              <AdjustmentsHorizontalIcon class="w-5 h-5 inline ml-2 rtl:ml-0 rtl:mr-2" />
              جستجوی پیشرفته
            </button>
          </div>
        </form>

        <!-- Advanced Search Panel -->
        <Transition
          enter-active-class="transition duration-300 ease-out"
          enter-from-class="transform -translate-y-4 opacity-0"
          enter-to-class="transform translate-y-0 opacity-100"
          leave-active-class="transition duration-250 ease-in"
          leave-from-class="transform translate-y-0 opacity-100"
          leave-to-class="transform -translate-y-4 opacity-0"
        >
          <div v-if="showAdvancedSearch" class="mt-8">
            <div class="card-neu max-w-4xl mx-auto">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">
                جستجوی پیشرفته
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Author Search -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    نویسنده
                  </label>
                  <input
                    v-model="advancedFilters.author"
                    type="text"
                    placeholder="نام نویسنده"
                    class="input-neu"
                  />
                </div>

                <!-- Publication Year -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    سال انتشار
                  </label>
                  <div class="flex space-x-2 rtl:space-x-reverse">
                    <input
                      v-model="advancedFilters.yearFrom"
                      type="number"
                      placeholder="از سال"
                      class="input-neu flex-1"
                      min="1900"
                      :max="currentYear"
                    />
                    <input
                      v-model="advancedFilters.yearTo"
                      type="number"
                      placeholder="تا سال"
                      class="input-neu flex-1"
                      min="1900"
                      :max="currentYear"
                    />
                  </div>
                </div>

                <!-- Category -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    دسته‌بندی
                  </label>
                  <select v-model="advancedFilters.category" class="input-neu">
                    <option value="">همه دسته‌بندی‌ها</option>
                    <option v-for="category in categories" :key="category.id" :value="category.id">
                      {{ category.name }}
                    </option>
                  </select>
                </div>

                <!-- Language -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    زبان
                  </label>
                  <select v-model="advancedFilters.language" class="input-neu">
                    <option value="">همه زبان‌ها</option>
                    <option value="fa">فارسی</option>
                    <option value="en">انگلیسی</option>
                    <option value="ar">عربی</option>
                  </select>
                </div>

                <!-- Journal -->
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    نشریه
                  </label>
                  <input
                    v-model="advancedFilters.journal"
                    type="text"
                    placeholder="نام نشریه یا مجله"
                    class="input-neu"
                  />
                </div>
              </div>

              <div class="flex justify-end space-x-4 rtl:space-x-reverse mt-6">
                <button
                  @click="resetAdvancedFilters"
                  type="button"
                  class="btn-neu-secondary"
                >
                  پاک کردن فیلترها
                </button>
                <button
                  @click="applyAdvancedSearch"
                  type="button"
                  class="btn-neu-primary"
                >
                  اعمال فیلترها
                </button>
              </div>
            </div>
          </div>
        </Transition>
      </div>

      <!-- Quick Search Tags -->
      <div class="mt-12 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">جستجوهای پرطرفدار:</p>
        <div class="flex flex-wrap justify-center gap-3">
          <button
            v-for="tag in popularTags"
            :key="tag"
            @click="searchQuery = tag; performSearch()"
            class="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-neu text-sm hover:shadow-neu-sm dark:hover:shadow-neu-dark-sm transition-all duration-200 focus-neu"
          >
            {{ tag }}
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/vue/24/outline'

// Router
const router = useRouter()

// Reactive state
const searchQuery = ref('')
const showSuggestions = ref(false)
const showAdvancedSearch = ref(false)
const selectedSuggestionIndex = ref(-1)
const searchInput = ref<HTMLInputElement>()

// Advanced search filters
const advancedFilters = ref({
  author: '',
  yearFrom: '',
  yearTo: '',
  category: '',
  language: '',
  journal: '',
})

// Mock data
const suggestions = ref([
  { text: 'هوش مصنوعی', count: 1250 },
  { text: 'یادگیری ماشین', count: 890 },
  { text: 'پردازش تصویر', count: 650 },
  { text: 'شبکه‌های عصبی', count: 420 },
  { text: 'داده‌کاوی', count: 380 },
])

const categories = [
  { id: 'computer-science', name: 'علوم کامپیوتر' },
  { id: 'engineering', name: 'مهندسی' },
  { id: 'medicine', name: 'پزشکی' },
  { id: 'physics', name: 'فیزیک' },
  { id: 'chemistry', name: 'شیمی' },
  { id: 'mathematics', name: 'ریاضی' },
]

const popularTags = [
  'هوش مصنوعی',
  'یادگیری ماشین',
  'بلاک چین',
  'اینترنت اشیا',
  'امنیت سایبری',
  'رباتیک',
  'نانوتکنولوژی',
  'بیوانفورماتیک'
]

// Computed
const currentYear = computed(() => new Date().getFullYear())

// Methods
const handleSearchInput = () => {
  if (searchQuery.value.length > 2) {
    showSuggestions.value = true
    // Here you would typically make an API call to get suggestions
  } else {
    showSuggestions.value = false
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || suggestions.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(
        selectedSuggestionIndex.value + 1,
        suggestions.value.length - 1
      )
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, -1)
      break
    case 'Enter':
      if (selectedSuggestionIndex.value >= 0) {
        event.preventDefault()
        selectSuggestion(suggestions.value[selectedSuggestionIndex.value])
      }
      break
    case 'Escape':
      showSuggestions.value = false
      selectedSuggestionIndex.value = -1
      break
  }
}

const selectSuggestion = (suggestion: { text: string; count?: number }) => {
  searchQuery.value = suggestion.text
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
  performSearch()
}

const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
  searchInput.value?.focus()
}

const performSearch = () => {
  if (!searchQuery.value.trim()) return
  
  showSuggestions.value = false
  
  // Navigate to search results page with query
  router.push({
    path: '/search',
    query: {
      q: searchQuery.value,
      ...Object.fromEntries(
        Object.entries(advancedFilters.value).filter(([_, value]) => value)
      )
    }
  })
}

const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

const resetAdvancedFilters = () => {
  advancedFilters.value = {
    author: '',
    yearFrom: '',
    yearTo: '',
    category: '',
    language: '',
    journal: '',
  }
}

const applyAdvancedSearch = () => {
  performSearch()
}

// Click outside to close suggestions
const handleClickOutside = (event: Event) => {
  if (searchInput.value && !searchInput.value.contains(event.target as Node)) {
    showSuggestions.value = false
    selectedSuggestionIndex.value = -1
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
