<template>
  <section class="py-16 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          مقالات برگزیده
        </h2>
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          مقالات پربازدید و تأثیرگذار در حوزه‌های مختلف علمی
        </p>
      </div>

      <!-- Filter Tabs -->
      <div class="flex flex-wrap justify-center gap-2 mb-8">
        <button
          v-for="filter in filters"
          :key="filter.id"
          @click="activeFilter = filter.id"
          :class="[
            'px-6 py-3 rounded-neu font-medium transition-all duration-200 focus-neu',
            activeFilter === filter.id
              ? 'bg-primary-500 text-white shadow-neu-inset'
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:shadow-neu-sm'
          ]"
        >
          {{ filter.name }}
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="i in 6"
          :key="i"
          class="card-neu animate-pulse"
        >
          <div class="h-48 bg-gray-200 dark:bg-gray-700 rounded-neu mb-4"></div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div class="space-y-2">
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Research Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <ResearchCard
          v-for="article in filteredArticles"
          :key="article.id"
          :article="article"
          @save="handleSave"
          @unsave="handleUnsave"
          @share="handleShare"
          @download="handleDownload"
        />
      </div>

      <!-- Empty State -->
      <div
        v-if="!isLoading && filteredArticles.length === 0"
        class="text-center py-12"
      >
        <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
          <DocumentTextIcon class="w-12 h-12 text-gray-400" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          مقاله‌ای یافت نشد
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          در حال حاضر مقاله‌ای در این دسته‌بندی موجود نیست.
        </p>
      </div>

      <!-- View All Button -->
      <div v-if="!isLoading && filteredArticles.length > 0" class="text-center mt-12">
        <router-link
          :to="`/search?category=${activeFilter}`"
          class="btn-neu-primary text-lg px-8 py-4 focus-neu"
        >
          مشاهده همه مقالات
          <ArrowLeftIcon class="w-5 h-5 inline mr-2 rtl:mr-0 rtl:ml-2 rtl:rotate-180" />
        </router-link>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ResearchCard from './ResearchCard.vue'
import {
  DocumentTextIcon,
  ArrowLeftIcon,
} from '@heroicons/vue/24/outline'

// Types
interface Author {
  id: string
  name: string
}

interface Article {
  id: string
  title: string
  abstract: string
  authors: Author[]
  category: string
  publishedAt: string
  journal?: string
  citationCount: number
  tags?: string[]
  thumbnail?: string
  downloadUrl?: string
  isSaved: boolean
}

// Reactive state
const isLoading = ref(true)
const activeFilter = ref('all')
const articles = ref<Article[]>([])

// Filter options
const filters = [
  { id: 'all', name: 'همه' },
  { id: 'computer-science', name: 'علوم کامپیوتر' },
  { id: 'engineering', name: 'مهندسی' },
  { id: 'medicine', name: 'پزشکی' },
  { id: 'physics', name: 'فیزیک' },
  { id: 'chemistry', name: 'شیمی' },
  { id: 'mathematics', name: 'ریاضی' },
]

// Computed
const filteredArticles = computed(() => {
  if (activeFilter.value === 'all') {
    return articles.value
  }
  return articles.value.filter(article => 
    article.category.toLowerCase().includes(activeFilter.value) ||
    getCategoryId(article.category) === activeFilter.value
  )
})

// Methods
const getCategoryId = (categoryName: string): string => {
  const categoryMap: Record<string, string> = {
    'علوم کامپیوتر': 'computer-science',
    'مهندسی': 'engineering',
    'پزشکی': 'medicine',
    'فیزیک': 'physics',
    'شیمی': 'chemistry',
    'ریاضی': 'mathematics',
  }
  return categoryMap[categoryName] || 'other'
}

const loadFeaturedArticles = async () => {
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock data
    articles.value = [
      {
        id: '1',
        title: 'کاربرد یادگیری عمیق در تشخیص بیماری‌های قلبی',
        abstract: 'این مطالعه به بررسی کاربرد الگوریتم‌های یادگیری عمیق در تشخیص زودهنگام بیماری‌های قلبی می‌پردازد. نتایج نشان می‌دهد که استفاده از شبکه‌های عصبی کانولوشنی می‌تواند دقت تشخیص را تا ۹۵٪ افزایش دهد.',
        authors: [
          { id: '1', name: 'دکتر علی احمدی' },
          { id: '2', name: 'دکتر فاطمه محمدی' }
        ],
        category: 'پزشکی',
        publishedAt: '2024-01-15',
        journal: 'مجله پزشکی ایران',
        citationCount: 45,
        tags: ['یادگیری عمیق', 'تشخیص پزشکی', 'بیماری قلبی'],
        thumbnail: 'https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=Medical+AI',
        downloadUrl: '/downloads/article-1.pdf',
        isSaved: false
      },
      {
        id: '2',
        title: 'بهینه‌سازی الگوریتم‌های مرتب‌سازی با استفاده از پردازش موازی',
        abstract: 'در این پژوهش، روش‌های جدیدی برای بهینه‌سازی الگوریتم‌های مرتب‌سازی ارائه شده است. با استفاده از تکنیک‌های پردازش موازی، سرعت اجرای الگوریتم‌ها تا ۳۰۰٪ بهبود یافته است.',
        authors: [
          { id: '3', name: 'دکتر محمد رضایی' },
          { id: '4', name: 'مهندس سارا کریمی' }
        ],
        category: 'علوم کامپیوتر',
        publishedAt: '2024-02-20',
        journal: 'نشریه علوم کامپیوتر ایران',
        citationCount: 32,
        tags: ['الگوریتم', 'پردازش موازی', 'بهینه‌سازی'],
        thumbnail: 'https://via.placeholder.com/400x300/10B981/FFFFFF?text=Computer+Science',
        downloadUrl: '/downloads/article-2.pdf',
        isSaved: true
      },
      {
        id: '3',
        title: 'طراحی و ساخت نانوحسگرهای زیستی برای تشخیص سرطان',
        abstract: 'این تحقیق به توسعه نانوحسگرهای زیستی نوین برای تشخیص زودهنگام سرطان می‌پردازد. نتایج آزمایشگاهی نشان‌دهنده حساسیت بالای ۹۸٪ این حسگرها است.',
        authors: [
          { id: '5', name: 'دکتر زهرا حسینی' },
          { id: '6', name: 'دکتر امیر نوری' }
        ],
        category: 'مهندسی',
        publishedAt: '2024-03-10',
        journal: 'مجله مهندسی زیست‌پزشکی',
        citationCount: 67,
        tags: ['نانوتکنولوژی', 'حسگر زیستی', 'تشخیص سرطان'],
        thumbnail: 'https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Nanotechnology',
        downloadUrl: '/downloads/article-3.pdf',
        isSaved: false
      },
      {
        id: '4',
        title: 'مطالعه خواص کوانتومی مواد دوبعدی گرافن',
        abstract: 'بررسی جامع خواص الکترونیکی و مغناطیسی گرافن در مقیاس نانو. این مطالعه نشان می‌دهد که گرافن پتانسیل بالایی برای کاربرد در الکترونیک کوانتومی دارد.',
        authors: [
          { id: '7', name: 'دکتر حسن علوی' },
          { id: '8', name: 'دکتر مریم صادقی' }
        ],
        category: 'فیزیک',
        publishedAt: '2024-01-25',
        journal: 'فیزیک ایران',
        citationCount: 89,
        tags: ['گرافن', 'فیزیک کوانتوم', 'مواد دوبعدی'],
        thumbnail: 'https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=Quantum+Physics',
        downloadUrl: '/downloads/article-4.pdf',
        isSaved: false
      },
      {
        id: '5',
        title: 'سنتز ترکیبات آلی جدید با خواص ضدسرطانی',
        abstract: 'در این پژوهش، ترکیبات آلی جدیدی با فعالیت ضدسرطانی بالا سنتز شده‌اند. آزمایش‌های in vitro نشان‌دهنده اثربخشی ۸۵٪ این ترکیبات است.',
        authors: [
          { id: '9', name: 'دکتر رضا مرادی' },
          { id: '10', name: 'دکتر لیلا احمدی' }
        ],
        category: 'شیمی',
        publishedAt: '2024-02-05',
        journal: 'شیمی دارویی ایران',
        citationCount: 54,
        tags: ['شیمی آلی', 'ضدسرطان', 'سنتز دارو'],
        thumbnail: 'https://via.placeholder.com/400x300/EF4444/FFFFFF?text=Chemistry',
        downloadUrl: '/downloads/article-5.pdf',
        isSaved: true
      },
      {
        id: '6',
        title: 'کاربرد نظریه گراف در بهینه‌سازی شبکه‌های حمل‌ونقل',
        abstract: 'این مطالعه روش‌های جدیدی بر اساس نظریه گراف برای بهینه‌سازی مسیرهای حمل‌ونقل شهری ارائه می‌دهد. نتایج نشان‌دهنده کاهش ۲۵٪ در زمان سفر است.',
        authors: [
          { id: '11', name: 'دکتر علی نژاد' },
          { id: '12', name: 'مهندس نرگس فرهادی' }
        ],
        category: 'ریاضی',
        publishedAt: '2024-03-01',
        journal: 'ریاضیات کاربردی ایران',
        citationCount: 28,
        tags: ['نظریه گراف', 'بهینه‌سازی', 'حمل‌ونقل'],
        thumbnail: 'https://via.placeholder.com/400x300/6366F1/FFFFFF?text=Mathematics',
        downloadUrl: '/downloads/article-6.pdf',
        isSaved: false
      }
    ]
  } catch (error) {
    console.error('Error loading featured articles:', error)
  } finally {
    isLoading.value = false
  }
}

const handleSave = (articleId: string) => {
  const article = articles.value.find(a => a.id === articleId)
  if (article) {
    article.isSaved = true
    // Here you would make an API call to save the article
    console.log('Saving article:', articleId)
  }
}

const handleUnsave = (articleId: string) => {
  const article = articles.value.find(a => a.id === articleId)
  if (article) {
    article.isSaved = false
    // Here you would make an API call to unsave the article
    console.log('Unsaving article:', articleId)
  }
}

const handleShare = (article: Article) => {
  // Implement sharing functionality
  if (navigator.share) {
    navigator.share({
      title: article.title,
      text: article.abstract,
      url: `${window.location.origin}/article/${article.id}`
    })
  } else {
    // Fallback for browsers that don't support Web Share API
    navigator.clipboard.writeText(`${window.location.origin}/article/${article.id}`)
    console.log('Article URL copied to clipboard')
  }
}

const handleDownload = (article: Article) => {
  if (article.downloadUrl) {
    window.open(article.downloadUrl, '_blank')
  }
}

// Lifecycle
onMounted(() => {
  loadFeaturedArticles()
})
</script>
