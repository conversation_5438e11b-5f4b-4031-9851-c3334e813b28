<script setup lang="ts">
import { ref } from 'vue'
import HeroSearch from '../components/search/HeroSearch.vue'
import FeaturedResearch from '../components/research/FeaturedResearch.vue'

// Categories data
const categories = [
  {
    id: 'computer-science',
    name: 'علوم کامپیوتر',
    icon: '💻',
    count: 15420,
    color: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
  },
  {
    id: 'engineering',
    name: 'مهندسی',
    icon: '⚙️',
    count: 12350,
    color: 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400'
  },
  {
    id: 'medicine',
    name: 'پزشکی',
    icon: '🏥',
    count: 18750,
    color: 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
  },
  {
    id: 'physics',
    name: 'فیزیک',
    icon: '⚛️',
    count: 9840,
    color: 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400'
  },
  {
    id: 'chemistry',
    name: 'شیم<PERSON>',
    icon: '🧪',
    count: 11200,
    color: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400'
  },
  {
    id: 'mathematics',
    name: 'ریاضی',
    icon: '📐',
    count: 7650,
    color: 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400'
  }
]

// Statistics data
const stats = [
  { value: '۱۲۵,۰۰۰+', label: 'مقاله علمی' },
  { value: '۸,۵۰۰+', label: 'نویسنده' },
  { value: '۲۵۰+', label: 'مجله' },
  { value: '۵۰+', label: 'دانشگاه' }
]

// Recent articles data
const recentArticles = [
  {
    id: '101',
    title: 'بررسی تأثیر هوش مصنوعی بر آینده پزشکی',
    authors: [
      { id: '1', name: 'دکتر علی احمدی' },
      { id: '2', name: 'دکتر سارا محمدی' }
    ],
    category: 'پزشکی',
    publishedAt: '2024-03-15',
    citationCount: 12
  },
  {
    id: '102',
    title: 'الگوریتم‌های بهینه‌سازی در شبکه‌های عصبی عمیق',
    authors: [
      { id: '3', name: 'دکتر محمد رضایی' }
    ],
    category: 'علوم کامپیوتر',
    publishedAt: '2024-03-14',
    citationCount: 8
  },
  {
    id: '103',
    title: 'کاربرد نانوتکنولوژی در صنعت خودروسازی',
    authors: [
      { id: '4', name: 'مهندس فاطمه کریمی' },
      { id: '5', name: 'دکتر حسن نوری' }
    ],
    category: 'مهندسی',
    publishedAt: '2024-03-13',
    citationCount: 15
  },
  {
    id: '104',
    title: 'مطالعه خواص کوانتومی مواد ابررسانا',
    authors: [
      { id: '6', name: 'دکتر زهرا علوی' }
    ],
    category: 'فیزیک',
    publishedAt: '2024-03-12',
    citationCount: 23
  },
  {
    id: '105',
    title: 'سنتز کاتالیست‌های سبز برای صنایع پتروشیمی',
    authors: [
      { id: '7', name: 'دکتر امیر حسینی' },
      { id: '8', name: 'دکتر مریم صادقی' }
    ],
    category: 'شیمی',
    publishedAt: '2024-03-11',
    citationCount: 7
  }
]

// Methods
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date)
}
</script>

<template>
  <div>
    <!-- Hero Search Section -->
    <HeroSearch />

    <!-- Featured Research Section -->
    <FeaturedResearch />

    <!-- Categories Section -->
    <section class="py-16 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            دسته‌بندی‌های علمی
          </h2>
          <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            مقالات را بر اساس حوزه‌های مختلف علمی مرور کنید
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <router-link
            v-for="category in categories"
            :key="category.id"
            :to="`/category/${category.id}`"
            class="category-card group focus-neu"
          >
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
              <div :class="[
                'w-12 h-12 rounded-neu flex items-center justify-center text-2xl',
                category.color
              ]">
                {{ category.icon }}
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
                  {{ category.name }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ category.count }} مقاله
                </p>
              </div>
            </div>
          </router-link>
        </div>
      </div>
    </section>

    <!-- Statistics Section -->
    <section class="py-16 bg-white dark:bg-gray-900">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            آمار پایگاه داده
          </h2>
          <p class="text-lg text-gray-600 dark:text-gray-400">
            نگاهی به آمار و ارقام پایگاه داده علمی سراج
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div v-for="stat in stats" :key="stat.label" class="stat-card">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Recent Publications Section -->
    <section class="py-16 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-8">
          <div>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
              جدیدترین مقالات
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400">
              آخرین مقالات منتشر شده در پایگاه داده
            </p>
          </div>
          <router-link
            to="/recent"
            class="btn-neu-secondary focus-neu"
          >
            مشاهده همه
          </router-link>
        </div>

        <div class="space-y-4">
          <div
            v-for="article in recentArticles"
            :key="article.id"
            class="card-neu hover:shadow-neu-lg transition-all duration-300"
          >
            <div class="flex items-start space-x-4 rtl:space-x-reverse">
              <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-neu flex items-center justify-center">
                  <span class="text-primary-600 dark:text-primary-400 font-bold text-lg">
                    {{ article.title.charAt(0) }}
                  </span>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <router-link
                  :to="`/article/${article.id}`"
                  class="text-lg font-semibold text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 focus-neu rounded"
                >
                  {{ article.title }}
                </router-link>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {{ article.authors.map(a => a.name).join('، ') }}
                </p>
                <div class="flex items-center space-x-4 rtl:space-x-reverse mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{{ formatDate(article.publishedAt) }}</span>
                  <span>{{ article.category }}</span>
                  <span>{{ article.citationCount }} استناد</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
