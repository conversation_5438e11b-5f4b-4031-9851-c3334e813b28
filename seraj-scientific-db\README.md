# سراج علمی - پایگاه داده علمی فارسی

پایگاه داده جامع مقالات و پژوهش‌های علمی به زبان فارسی با طراحی مدرن و قابلیت‌های پیشرفته جستجو.

## ویژگی‌ها

### 🎨 طراحی و رابط کاربری
- **طراحی نئومورفیسم**: استفاده از سایه‌های نرم و گرادیان‌های ظریف
- **پشتیبانی از RTL**: طراحی کامل برای زبان فارسی
- **حالت تاریک**: پشتیبانی کامل از حالت روشن و تاریک
- **طراحی واکنش‌گرا**: سازگار با موبایل، تبلت و دسکتاپ
- **فونت وزیرمتن**: استفاده از فونت اختصاصی فارسی

### 🔍 جستجو و فیلتر
- **جستجوی هوشمند**: جستجو در عنوان، نویسنده، کلیدواژه و چکیده
- **پیشنهادات خودکار**: نمایش پیشنهادات جستجو در زمان واقعی
- **جستجوی پیشرفته**: فیلتر بر اساس نویسنده، سال، دسته‌بندی و نشریه
- **برچسب‌های محبوب**: دسترسی سریع به جستجوهای پرطرفدار

### 📚 مدیریت محتوا
- **مقالات برگزیده**: نمایش مقالات پربازدید و تأثیرگذار
- **دسته‌بندی علمی**: تقسیم‌بندی بر اساس حوزه‌های مختلف علمی
- **آمار و گزارش**: نمایش آمار کامل پایگاه داده
- **مقالات جدید**: نمایش آخرین مقالات منتشر شده

### ♿ دسترسی‌پذیری
- **WCAG 2.1 AA**: تطبیق کامل با استانداردهای دسترسی‌پذیری
- **پشتیبانی از صفحه‌خوان**: سازگار با نرم‌افزارهای کمک‌رسان
- **ناوبری کیبورد**: امکان استفاده کامل با کیبورد
- **کنتراست مناسب**: رنگ‌بندی مناسب برای کاربران کم‌بینا

## تکنولوژی‌های استفاده شده

### Frontend Framework
- **Vue 3**: با Composition API و `<script setup>`
- **TypeScript**: برای type safety و بهتر شدن تجربه توسعه
- **Vite**: ابزار build سریع و مدرن

### UI/UX
- **TailwindCSS**: فریمورک CSS utility-first
- **HeadlessUI**: کامپوننت‌های accessible و unstyled
- **Heroicons**: آیکون‌های مدرن و زیبا
- **TailwindCSS RTL**: پلاگین پشتیبانی از RTL

### State Management & Routing
- **Pinia**: مدیریت state مدرن برای Vue
- **Vue Router**: مسیریابی SPA

### Testing & Quality
- **Vitest**: فریمورک تست سریع
- **ESLint**: linting و کیفیت کد
- **Prettier**: فرمت‌دهی خودکار کد

## نصب و راه‌اندازی

### پیش‌نیازها
- Node.js 20.19+ یا 22.12+
- npm یا yarn

### مراحل نصب

1. **کلون کردن پروژه**
```bash
git clone <repository-url>
cd seraj-scientific-db
```

2. **نصب وابستگی‌ها**
```bash
npm install
```

3. **اجرای سرور توسعه**
```bash
npm run dev
```

4. **ساخت برای production**
```bash
npm run build
```

5. **پیش‌نمایش build**
```bash
npm run preview
```

## ساختار پروژه

```
src/
├── components/          # کامپوننت‌های Vue
│   ├── layout/         # کامپوننت‌های layout
│   ├── search/         # کامپوننت‌های جستجو
│   ├── research/       # کامپوننت‌های مقالات
│   └── ui/            # کامپوننت‌های UI عمومی
├── views/              # صفحات اصلی
├── data/               # داده‌های mock و types
├── assets/             # فایل‌های استاتیک
└── styles/             # فایل‌های CSS
```

## کامپوننت‌های اصلی

### Layout Components
- **AppHeader**: هدر اصلی با منوی ناوبری
- **AppFooter**: فوتر با لینک‌ها و اطلاعات تماس
- **AppLayout**: wrapper اصلی layout

### Search Components
- **HeroSearch**: بخش جستجوی اصلی صفحه
- **SearchSuggestions**: پیشنهادات جستجو
- **AdvancedSearch**: فرم جستجوی پیشرفته

### Research Components
- **ResearchCard**: کارت نمایش مقاله
- **FeaturedResearch**: بخش مقالات برگزیده
- **CategoryGrid**: شبکه دسته‌بندی‌ها

**سراج علمی** - روشن کردن راه علم و دانش 🔬✨
