<template>
  <header class="bg-white dark:bg-gray-900 shadow-neu dark:shadow-neu-dark sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo and Brand -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-3 rtl:space-x-reverse">
            <div class="w-10 h-10 bg-primary-500 rounded-neu flex items-center justify-center shadow-neu-sm">
              <span class="text-white font-bold text-xl">س</span>
            </div>
            <div class="hidden sm:block">
              <h1 class="text-xl font-bold text-gray-900 dark:text-white">سراج علمی</h1>
              <p class="text-xs text-gray-500 dark:text-gray-400">پایگاه داده علمی</p>
            </div>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.href"
            class="nav-item focus-neu"
            :class="{ 'active': $route.path === item.href }"
          >
            {{ item.name }}
          </router-link>
        </nav>

        <!-- Right side actions -->
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
          <!-- Language Toggle -->
          <button
            @click="toggleLanguage"
            class="p-2 rounded-neu text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus-neu"
            :title="currentLanguage === 'fa' ? 'English' : 'فارسی'"
          >
            <span class="text-sm font-medium">{{ currentLanguage === 'fa' ? 'EN' : 'فا' }}</span>
          </button>

          <!-- Dark Mode Toggle -->
          <button
            @click="toggleDarkMode"
            class="p-2 rounded-neu text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus-neu"
            :title="isDark ? 'حالت روشن' : 'حالت تاریک'"
          >
            <SunIcon v-if="isDark" class="w-5 h-5" />
            <MoonIcon v-else class="w-5 h-5" />
          </button>

          <!-- User Menu -->
          <div class="relative">
            <Menu as="div" class="relative inline-block text-right">
              <div>
                <MenuButton class="flex items-center p-2 rounded-neu text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus-neu">
                  <UserIcon class="w-5 h-5" />
                  <ChevronDownIcon class="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                </MenuButton>
              </div>

              <transition
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="transform scale-95 opacity-0"
                enter-to-class="transform scale-100 opacity-100"
                leave-active-class="transition duration-75 ease-in"
                leave-from-class="transform scale-100 opacity-100"
                leave-to-class="transform scale-95 opacity-0"
              >
                <MenuItems class="absolute left-0 rtl:left-auto rtl:right-0 mt-2 w-56 origin-top-right bg-white dark:bg-gray-800 rounded-neu-lg shadow-neu-lg dark:shadow-neu-dark-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div class="py-1">
                    <MenuItem v-for="item in userMenuItems" :key="item.name" v-slot="{ active }">
                      <a
                        :href="item.href"
                        :class="[
                          active ? 'bg-gray-100 dark:bg-gray-700' : '',
                          'block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        ]"
                      >
                        {{ item.name }}
                      </a>
                    </MenuItem>
                  </div>
                </MenuItems>
              </transition>
            </Menu>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden p-2 rounded-neu text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus-neu"
          >
            <Bars3Icon v-if="!isMobileMenuOpen" class="w-6 h-6" />
            <XMarkIcon v-else class="w-6 h-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <transition
        enter-active-class="transition duration-200 ease-out"
        enter-from-class="transform -translate-y-2 opacity-0"
        enter-to-class="transform translate-y-0 opacity-100"
        leave-active-class="transition duration-150 ease-in"
        leave-from-class="transform translate-y-0 opacity-100"
        leave-to-class="transform -translate-y-2 opacity-0"
      >
        <div v-show="isMobileMenuOpen" class="md:hidden">
          <div class="px-2 pt-2 pb-3 space-y-1 bg-gray-50 dark:bg-gray-800 rounded-neu-lg mt-2 shadow-neu-inset dark:shadow-neu-dark-inset">
            <router-link
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.href"
              class="block px-3 py-2 rounded-neu text-base font-medium text-gray-700 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              @click="closeMobileMenu"
            >
              {{ item.name }}
            </router-link>
          </div>
        </div>
      </transition>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import {
  SunIcon,
  MoonIcon,
  UserIcon,
  ChevronDownIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'

// Reactive state
const isMobileMenuOpen = ref(false)
const isDark = ref(false)
const currentLanguage = ref('fa')

// Navigation items
const navigationItems = [
  { name: 'خانه', href: '/' },
  { name: 'جستجو', href: '/search' },
  { name: 'دسته‌بندی‌ها', href: '/categories' },
  { name: 'مقالات جدید', href: '/recent' },
  { name: 'درباره ما', href: '/about' },
]

// User menu items
const userMenuItems = [
  { name: 'پروفایل', href: '/profile' },
  { name: 'مقالات ذخیره شده', href: '/saved' },
  { name: 'تنظیمات', href: '/settings' },
  { name: 'خروج', href: '/logout' },
]

// Methods
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

const toggleDarkMode = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('darkMode', isDark.value.toString())
}

const toggleLanguage = () => {
  currentLanguage.value = currentLanguage.value === 'fa' ? 'en' : 'fa'
  // Here you would implement language switching logic
}

// Initialize dark mode from localStorage
if (typeof window !== 'undefined') {
  const savedDarkMode = localStorage.getItem('darkMode')
  if (savedDarkMode) {
    isDark.value = savedDarkMode === 'true'
    document.documentElement.classList.toggle('dark', isDark.value)
  }
}
</script>
