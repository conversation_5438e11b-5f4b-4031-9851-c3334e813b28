<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Skip to main content link for accessibility -->
    <a
      href="#main-content"
      class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 rtl:left-auto rtl:right-4 bg-primary-600 text-white px-4 py-2 rounded-neu z-50 focus-neu"
    >
      پرش به محتوای اصلی
    </a>

    <!-- Header -->
    <AppHeader />

    <!-- Main Content -->
    <main id="main-content" class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <AppFooter />

    <!-- Loading Overlay -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isLoading"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        role="dialog"
        aria-modal="true"
        aria-labelledby="loading-title"
      >
        <div class="bg-white dark:bg-gray-800 rounded-neu-lg p-6 shadow-neu-xl dark:shadow-neu-dark-xl">
          <div class="flex items-center space-x-3 rtl:space-x-reverse">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
            <span id="loading-title" class="text-gray-900 dark:text-white font-medium">
              در حال بارگذاری...
            </span>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Scroll to Top Button -->
    <Transition
      enter-active-class="transition-all duration-300"
      enter-from-class="opacity-0 transform translate-y-2"
      enter-to-class="opacity-100 transform translate-y-0"
      leave-active-class="transition-all duration-300"
      leave-from-class="opacity-100 transform translate-y-0"
      leave-to-class="opacity-0 transform translate-y-2"
    >
      <button
        v-if="showScrollToTop"
        @click="scrollToTop"
        class="fixed bottom-6 left-6 rtl:left-auto rtl:right-6 p-3 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-neu-lg hover:shadow-neu-xl transition-all duration-200 focus-neu z-40"
        title="بازگشت به بالا"
        aria-label="بازگشت به بالای صفحه"
      >
        <ChevronUpIcon class="w-6 h-6" />
      </button>
    </Transition>

    <!-- Toast Notifications -->
    <div
      class="fixed top-4 left-4 rtl:left-auto rtl:right-4 z-50 space-y-2"
      aria-live="polite"
      aria-label="اعلان‌ها"
    >
      <TransitionGroup
        enter-active-class="transition-all duration-300"
        enter-from-class="opacity-0 transform translate-x-full rtl:-translate-x-full"
        enter-to-class="opacity-100 transform translate-x-0"
        leave-active-class="transition-all duration-300"
        leave-from-class="opacity-100 transform translate-x-0"
        leave-to-class="opacity-0 transform translate-x-full rtl:-translate-x-full"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="[
            'max-w-sm w-full rounded-neu-lg shadow-neu-lg p-4',
            {
              'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200': toast.type === 'success',
              'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200': toast.type === 'error',
              'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200': toast.type === 'warning',
              'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200': toast.type === 'info',
            }
          ]"
        >
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <CheckCircleIcon v-if="toast.type === 'success'" class="w-5 h-5" />
              <XCircleIcon v-else-if="toast.type === 'error'" class="w-5 h-5" />
              <ExclamationTriangleIcon v-else-if="toast.type === 'warning'" class="w-5 h-5" />
              <InformationCircleIcon v-else class="w-5 h-5" />
            </div>
            <div class="mr-3 rtl:mr-0 rtl:ml-3 flex-1">
              <p class="text-sm font-medium">{{ toast.title }}</p>
              <p v-if="toast.message" class="text-sm mt-1 opacity-90">{{ toast.message }}</p>
            </div>
            <button
              @click="removeToast(toast.id)"
              class="mr-2 rtl:mr-0 rtl:ml-2 flex-shrink-0 p-1 rounded-full hover:bg-black hover:bg-opacity-10 focus-neu"
              :aria-label="`بستن اعلان ${toast.title}`"
            >
              <XMarkIcon class="w-4 h-4" />
            </button>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
import {
  ChevronUpIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'

// Props
interface Props {
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

// Reactive state
const showScrollToTop = ref(false)
const toasts = ref<Array<{
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
}>>([])

// Scroll to top functionality
const handleScroll = () => {
  showScrollToTop.value = window.scrollY > 300
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// Toast notification system
const addToast = (toast: Omit<typeof toasts.value[0], 'id'>) => {
  const id = Date.now().toString()
  toasts.value.push({ ...toast, id })
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    removeToast(id)
  }, 5000)
}

const removeToast = (id: string) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    toasts.value.splice(index, 1)
  }
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  
  // Initialize dark mode from system preference if not set
  if (!localStorage.getItem('darkMode')) {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    document.documentElement.classList.toggle('dark', prefersDark)
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

// Expose methods for parent components
defineExpose({
  addToast,
  removeToast
})
</script>
