// Types
export interface Author {
  id: string
  name: string
  affiliation?: string
  email?: string
  orcid?: string
}

export interface Article {
  id: string
  title: string
  abstract: string
  authors: Author[]
  category: string
  publishedAt: string
  journal?: string
  citationCount: number
  tags?: string[]
  thumbnail?: string
  downloadUrl?: string
  isSaved: boolean
  doi?: string
  pages?: string
  volume?: string
  issue?: string
}

export interface Category {
  id: string
  name: string
  icon: string
  count: number
  color: string
  description?: string
}

export interface Journal {
  id: string
  name: string
  issn: string
  publisher: string
  impactFactor?: number
}

export interface University {
  id: string
  name: string
  city: string
  country: string
  website?: string
}

// Mock Authors
export const mockAuthors: <AUTHORS>
  {
    id: '1',
    name: 'دکتر علی احمدی',
    affiliation: 'دانشگاه تهران',
    email: '<EMAIL>',
    orcid: '0000-0001-2345-6789'
  },
  {
    id: '2',
    name: 'دکتر فاطمه محمدی',
    affiliation: 'دانشگاه شریف',
    email: 'f.moh<PERSON><PERSON><PERSON>@sharif.edu'
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON><PERSON> محمد رضایی',
    affiliation: 'دانشگاه امیرکبیر',
    email: '<EMAIL>'
  },
  {
    id: '4',
    name: 'مهندس سارا کریمی',
    affiliation: 'دانشگاه صنعتی اصفهان',
    email: '<EMAIL>'
  },
  {
    id: '5',
    name: 'دکتر زهرا حسینی',
    affiliation: 'دانشگاه علوم پزشکی تهران',
    email: '<EMAIL>'
  },
  {
    id: '6',
    name: 'دکتر امیر نوری',
    affiliation: 'دانشگاه فردوسی مشهد',
    email: '<EMAIL>'
  },
  {
    id: '7',
    name: 'دکتر حسن علوی',
    affiliation: 'دانشگاه شیراز',
    email: '<EMAIL>'
  },
  {
    id: '8',
    name: 'دکتر مریم صادقی',
    affiliation: 'دانشگاه تبریز',
    email: '<EMAIL>'
  },
  {
    id: '9',
    name: 'دکتر رضا مرادی',
    affiliation: 'دانشگاه اصفهان',
    email: '<EMAIL>'
  },
  {
    id: '10',
    name: 'دکتر لیلا احمدی',
    affiliation: 'دانشگاه علم و صنعت',
    email: '<EMAIL>'
  }
]

// Mock Categories
export const mockCategories: Category[] = [
  {
    id: 'computer-science',
    name: 'علوم کامپیوتر',
    icon: '💻',
    count: 15420,
    color: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    description: 'هوش مصنوعی، یادگیری ماشین، الگوریتم‌ها و ساختمان داده'
  },
  {
    id: 'engineering',
    name: 'مهندسی',
    icon: '⚙️',
    count: 12350,
    color: 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400',
    description: 'مهندسی برق، مکانیک، عمران و شیمی'
  },
  {
    id: 'medicine',
    name: 'پزشکی',
    icon: '🏥',
    count: 18750,
    color: 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400',
    description: 'پزشکی بالینی، داروسازی و علوم زیست‌پزشکی'
  },
  {
    id: 'physics',
    name: 'فیزیک',
    icon: '⚛️',
    count: 9840,
    color: 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400',
    description: 'فیزیک نظری، کاربردی و فیزیک کوانتوم'
  },
  {
    id: 'chemistry',
    name: 'شیمی',
    icon: '🧪',
    count: 11200,
    color: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    description: 'شیمی آلی، معدنی و شیمی تجزیه'
  },
  {
    id: 'mathematics',
    name: 'ریاضی',
    icon: '📐',
    count: 7650,
    color: 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400',
    description: 'ریاضی محض، کاربردی و آمار'
  }
]

// Mock Journals
export const mockJournals: Journal[] = [
  {
    id: '1',
    name: 'مجله پزشکی ایران',
    issn: '1735-9694',
    publisher: 'انجمن پزشکی ایران',
    impactFactor: 2.45
  },
  {
    id: '2',
    name: 'نشریه علوم کامپیوتر ایران',
    issn: '2008-8310',
    publisher: 'انجمن علوم کامپیوتر ایران',
    impactFactor: 1.87
  },
  {
    id: '3',
    name: 'مجله مهندسی زیست‌پزشکی',
    issn: '2251-8363',
    publisher: 'دانشگاه علوم پزشکی تهران',
    impactFactor: 3.12
  },
  {
    id: '4',
    name: 'فیزیک ایران',
    issn: '1735-9325',
    publisher: 'انجمن فیزیک ایران',
    impactFactor: 1.65
  },
  {
    id: '5',
    name: 'شیمی دارویی ایران',
    issn: '2008-2746',
    publisher: 'انجمن شیمی ایران',
    impactFactor: 2.89
  }
]

// Mock Universities
export const mockUniversities: University[] = [
  {
    id: '1',
    name: 'دانشگاه تهران',
    city: 'تهران',
    country: 'ایران',
    website: 'https://ut.ac.ir'
  },
  {
    id: '2',
    name: 'دانشگاه صنعتی شریف',
    city: 'تهران',
    country: 'ایران',
    website: 'https://sharif.edu'
  },
  {
    id: '3',
    name: 'دانشگاه امیرکبیر',
    city: 'تهران',
    country: 'ایران',
    website: 'https://aut.ac.ir'
  },
  {
    id: '4',
    name: 'دانشگاه صنعتی اصفهان',
    city: 'اصفهان',
    country: 'ایران',
    website: 'https://iut.ac.ir'
  },
  {
    id: '5',
    name: 'دانشگاه فردوسی مشهد',
    city: 'مشهد',
    country: 'ایران',
    website: 'https://um.ac.ir'
  }
]

// Popular search terms
export const popularSearchTerms = [
  'هوش مصنوعی',
  'یادگیری ماشین',
  'بلاک چین',
  'اینترنت اشیا',
  'امنیت سایبری',
  'رباتیک',
  'نانوتکنولوژی',
  'بیوانفورماتیک',
  'انرژی تجدیدپذیر',
  'محاسبات کوانتوم'
]

// Statistics
export const siteStatistics = {
  totalArticles: 125000,
  totalAuthors: <AUTHORS>
  totalJournals: 250,
  totalUniversities: 50,
  monthlyVisitors: 45000,
  dailyDownloads: 1200
}

// Recent activity
export const recentActivity = [
  {
    type: 'new_article',
    title: 'مقاله جدید در حوزه هوش مصنوعی منتشر شد',
    timestamp: '2024-03-15T10:30:00Z'
  },
  {
    type: 'author_joined',
    title: 'دکتر احمد محمدی به پایگاه داده پیوست',
    timestamp: '2024-03-15T09:15:00Z'
  },
  {
    type: 'journal_added',
    title: 'مجله جدید "علوم داده ایران" اضافه شد',
    timestamp: '2024-03-14T16:45:00Z'
  }
]

// Helper functions
export const getAuthorById = (id: string): Author | undefined => {
  return mockAuthors.find(author => author.id === id)
}

export const getCategoryById = (id: string): Category | undefined => {
  return mockCategories.find(category => category.id === id)
}

export const getJournalById = (id: string): Journal | undefined => {
  return mockJournals.find(journal => journal.id === id)
}

export const getUniversityById = (id: string): University | undefined => {
  return mockUniversities.find(university => university.id === id)
}

export const formatPersianNumber = (num: number): string => {
  const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹']
  return num.toString().replace(/\d/g, (digit) => persianDigits[parseInt(digit)])
}

export const formatPersianDate = (dateString: string): string => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date)
}

// Mock Articles
export const mockArticles: Article[] = [
  {
    id: '1',
    title: 'کاربرد یادگیری عمیق در تشخیص بیماری‌های قلبی',
    abstract: 'این مطالعه به بررسی کاربرد الگوریتم‌های یادگیری عمیق در تشخیص زودهنگام بیماری‌های قلبی می‌پردازد. نتایج نشان می‌دهد که استفاده از شبکه‌های عصبی کانولوشنی می‌تواند دقت تشخیص را تا ۹۵٪ افزایش دهد. در این پژوهش، مجموعه داده‌ای شامل ۱۰,۰۰۰ تصویر ECG از بیماران مختلف مورد استفاده قرار گرفت.',
    authors: [mockAuthors[0], mockAuthors[1]],
    category: 'پزشکی',
    publishedAt: '2024-01-15',
    journal: 'مجله پزشکی ایران',
    citationCount: 45,
    tags: ['یادگیری عمیق', 'تشخیص پزشکی', 'بیماری قلبی', 'CNN', 'ECG'],
    thumbnail: 'https://via.placeholder.com/400x300/EF4444/FFFFFF?text=Medical+AI',
    downloadUrl: '/downloads/article-1.pdf',
    isSaved: false,
    doi: '10.1234/ijm.2024.001',
    pages: '15-28',
    volume: '45',
    issue: '2'
  },
  {
    id: '2',
    title: 'بهینه‌سازی الگوریتم‌های مرتب‌سازی با استفاده از پردازش موازی',
    abstract: 'در این پژوهش، روش‌های جدیدی برای بهینه‌سازی الگوریتم‌های مرتب‌سازی ارائه شده است. با استفاده از تکنیک‌های پردازش موازی و معماری GPU، سرعت اجرای الگوریتم‌ها تا ۳۰۰٪ بهبود یافته است. الگوریتم‌های Quick Sort، Merge Sort و Heap Sort مورد بررسی قرار گرفتند.',
    authors: [mockAuthors[2], mockAuthors[3]],
    category: 'علوم کامپیوتر',
    publishedAt: '2024-02-20',
    journal: 'نشریه علوم کامپیوتر ایران',
    citationCount: 32,
    tags: ['الگوریتم', 'پردازش موازی', 'بهینه‌سازی', 'GPU', 'مرتب‌سازی'],
    thumbnail: 'https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=Computer+Science',
    downloadUrl: '/downloads/article-2.pdf',
    isSaved: true,
    doi: '10.1234/ijcs.2024.002',
    pages: '45-62',
    volume: '12',
    issue: '1'
  },
  {
    id: '3',
    title: 'طراحی و ساخت نانوحسگرهای زیستی برای تشخیص سرطان',
    abstract: 'این تحقیق به توسعه نانوحسگرهای زیستی نوین برای تشخیص زودهنگام سرطان می‌پردازد. نتایج آزمایشگاهی نشان‌دهنده حساسیت بالای ۹۸٪ این حسگرها است. از نانوذرات طلا و آنتی‌بادی‌های اختصاصی برای ساخت این حسگرها استفاده شده است.',
    authors: [mockAuthors[4], mockAuthors[5]],
    category: 'مهندسی',
    publishedAt: '2024-03-10',
    journal: 'مجله مهندسی زیست‌پزشکی',
    citationCount: 67,
    tags: ['نانوتکنولوژی', 'حسگر زیستی', 'تشخیص سرطان', 'نانوذرات طلا', 'آنتی‌بادی'],
    thumbnail: 'https://via.placeholder.com/400x300/10B981/FFFFFF?text=Nanotechnology',
    downloadUrl: '/downloads/article-3.pdf',
    isSaved: false,
    doi: '10.1234/jbme.2024.003',
    pages: '78-95',
    volume: '8',
    issue: '3'
  },
  {
    id: '4',
    title: 'مطالعه خواص کوانتومی مواد دوبعدی گرافن',
    abstract: 'بررسی جامع خواص الکترونیکی و مغناطیسی گرافن در مقیاس نانو. این مطالعه نشان می‌دهد که گرافن پتانسیل بالایی برای کاربرد در الکترونیک کوانتومی دارد. خواص منحصر به فرد گرافن شامل رسانایی بالا و شفافیت نوری مورد بحث قرار گرفته است.',
    authors: [mockAuthors[6], mockAuthors[7]],
    category: 'فیزیک',
    publishedAt: '2024-01-25',
    journal: 'فیزیک ایران',
    citationCount: 89,
    tags: ['گرافن', 'فیزیک کوانتوم', 'مواد دوبعدی', 'الکترونیک', 'نانومواد'],
    thumbnail: 'https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=Quantum+Physics',
    downloadUrl: '/downloads/article-4.pdf',
    isSaved: false,
    doi: '10.1234/ip.2024.004',
    pages: '112-135',
    volume: '28',
    issue: '4'
  },
  {
    id: '5',
    title: 'سنتز ترکیبات آلی جدید با خواص ضدسرطانی',
    abstract: 'در این پژوهش، ترکیبات آلی جدیدی با فعالیت ضدسرطانی بالا سنتز شده‌اند. آزمایش‌های in vitro نشان‌دهنده اثربخشی ۸۵٪ این ترکیبات است. مکانیسم عمل این ترکیبات بر اساس مهار آنزیم‌های کلیدی در تکثیر سلول‌های سرطانی است.',
    authors: [mockAuthors[8], mockAuthors[9]],
    category: 'شیمی',
    publishedAt: '2024-02-05',
    journal: 'شیمی دارویی ایران',
    citationCount: 54,
    tags: ['شیمی آلی', 'ضدسرطان', 'سنتز دارو', 'آزمایش in vitro', 'مهار آنزیمی'],
    thumbnail: 'https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Chemistry',
    downloadUrl: '/downloads/article-5.pdf',
    isSaved: true,
    doi: '10.1234/ipc.2024.005',
    pages: '203-220',
    volume: '15',
    issue: '2'
  }
]
