<template>
  <footer class="bg-gray-100 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Brand Section -->
        <div class="lg:col-span-1">
          <div class="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <div class="w-10 h-10 bg-primary-500 rounded-neu flex items-center justify-center shadow-neu-sm">
              <span class="text-white font-bold text-xl">س</span>
            </div>
            <div>
              <h3 class="text-lg font-bold text-gray-900 dark:text-white">سراج علمی</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">پایگاه داده علمی</p>
            </div>
          </div>
          <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed mb-4">
            پایگاه داده جامع مقالات و پژوهش‌های علمی به زبان فارسی. دسترسی آسان به هزاران مقاله علمی در حوزه‌های مختلف.
          </p>
          <div class="flex space-x-4 rtl:space-x-reverse">
            <a
              v-for="social in socialLinks"
              :key="social.name"
              :href="social.href"
              class="p-2 rounded-neu text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors duration-200 focus-neu"
              :title="social.name"
            >
              <component :is="social.icon" class="w-5 h-5" />
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
            دسترسی سریع
          </h3>
          <ul class="space-y-3">
            <li v-for="link in quickLinks" :key="link.name">
              <router-link
                :to="link.href"
                class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 text-sm"
              >
                {{ link.name }}
              </router-link>
            </li>
          </ul>
        </div>

        <!-- Categories -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
            دسته‌بندی‌ها
          </h3>
          <ul class="space-y-3">
            <li v-for="category in categories" :key="category.name">
              <router-link
                :to="category.href"
                class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 text-sm"
              >
                {{ category.name }}
              </router-link>
            </li>
          </ul>
        </div>

        <!-- Contact & Support -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
            پشتیبانی
          </h3>
          <ul class="space-y-3">
            <li v-for="support in supportLinks" :key="support.name">
              <a
                :href="support.href"
                class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 text-sm"
              >
                {{ support.name }}
              </a>
            </li>
          </ul>
          
          <!-- Newsletter Signup -->
          <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
              خبرنامه
            </h4>
            <form @submit.prevent="subscribeNewsletter" class="flex">
              <input
                v-model="email"
                type="email"
                placeholder="ایمیل شما"
                class="input-neu flex-1 text-sm py-2 px-3 ml-2 rtl:ml-0 rtl:mr-2"
                required
              />
              <button
                type="submit"
                class="btn-neu-primary text-sm px-4 py-2"
                :disabled="isSubscribing"
              >
                {{ isSubscribing ? 'در حال ارسال...' : 'عضویت' }}
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Statistics Section -->
      <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div v-for="stat in stats" :key="stat.label" class="text-center">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="text-sm text-gray-500 dark:text-gray-400 mb-4 md:mb-0">
            © {{ currentYear }} سراج علمی. تمامی حقوق محفوظ است.
          </div>
          <div class="flex space-x-6 rtl:space-x-reverse">
            <a
              v-for="legal in legalLinks"
              :key="legal.name"
              :href="legal.href"
              class="text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
            >
              {{ legal.name }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Reactive state
const email = ref('')
const isSubscribing = ref(false)

// Current year
const currentYear = computed(() => new Date().getFullYear())

// Social media links (placeholder icons - you'll need to import actual icons)
const socialLinks = [
  { name: 'تلگرام', href: '#', icon: 'div' },
  { name: 'اینستاگرام', href: '#', icon: 'div' },
  { name: 'توییتر', href: '#', icon: 'div' },
  { name: 'لینکدین', href: '#', icon: 'div' },
]

// Quick links
const quickLinks = [
  { name: 'جستجوی پیشرفته', href: '/advanced-search' },
  { name: 'مقالات پربازدید', href: '/popular' },
  { name: 'نویسندگان برتر', href: '/top-authors' },
  { name: 'مجلات علمی', href: '/journals' },
  { name: 'کنفرانس‌ها', href: '/conferences' },
]

// Categories
const categories = [
  { name: 'علوم پزشکی', href: '/category/medical' },
  { name: 'مهندسی', href: '/category/engineering' },
  { name: 'علوم کامپیوتر', href: '/category/computer-science' },
  { name: 'علوم پایه', href: '/category/basic-sciences' },
  { name: 'علوم انسانی', href: '/category/humanities' },
]

// Support links
const supportLinks = [
  { name: 'راهنمای استفاده', href: '/help' },
  { name: 'سوالات متداول', href: '/faq' },
  { name: 'تماس با ما', href: '/contact' },
  { name: 'گزارش مشکل', href: '/report' },
]

// Legal links
const legalLinks = [
  { name: 'حریم خصوصی', href: '/privacy' },
  { name: 'شرایط استفاده', href: '/terms' },
  { name: 'کوکی‌ها', href: '/cookies' },
]

// Statistics
const stats = [
  { value: '۱۲۵,۰۰۰+', label: 'مقاله علمی' },
  { value: '۸,۵۰۰+', label: 'نویسنده' },
  { value: '۲۵۰+', label: 'مجله' },
  { value: '۵۰+', label: 'دانشگاه' },
]

// Methods
const subscribeNewsletter = async () => {
  if (!email.value) return
  
  isSubscribing.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Handle newsletter subscription
    console.log('Newsletter subscription:', email.value)
    email.value = ''
    // Show success message
  } catch (error) {
    console.error('Newsletter subscription error:', error)
    // Show error message
  } finally {
    isSubscribing.value = false
  }
}
</script>
