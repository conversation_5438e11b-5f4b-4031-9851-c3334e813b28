<template>
  <article class="research-card group">
    <!-- Article Image/Thumbnail -->
    <div v-if="article.thumbnail" class="relative overflow-hidden rounded-neu mb-4">
      <img
        :src="article.thumbnail"
        :alt="article.title"
        class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
        loading="lazy"
      />
      <div class="absolute top-3 right-3 rtl:right-auto rtl:left-3">
        <span
          :class="[
            'px-2 py-1 text-xs font-medium rounded-full',
            getCategoryColor(article.category)
          ]"
        >
          {{ article.category }}
        </span>
      </div>
    </div>

    <!-- Article Content -->
    <div class="space-y-3">
      <!-- Title -->
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
        <router-link :to="`/article/${article.id}`" class="focus-neu rounded">
          {{ article.title }}
        </router-link>
      </h3>

      <!-- Authors -->
      <div class="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
        <UserIcon class="w-4 h-4 flex-shrink-0" />
        <span class="line-clamp-1">
          <template v-for="(author, index) in article.authors" :key="author.id">
            <router-link
              :to="`/author/${author.id}`"
              class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 focus-neu rounded"
            >
              {{ author.name }}
            </router-link>
            <span v-if="index < article.authors.length - 1">، </span>
          </template>
        </span>
      </div>

      <!-- Abstract -->
      <p class="text-gray-700 dark:text-gray-300 text-sm line-clamp-3 leading-relaxed">
        {{ article.abstract }}
      </p>

      <!-- Metadata -->
      <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
          <!-- Publication Date -->
          <div class="flex items-center space-x-1 rtl:space-x-reverse">
            <CalendarIcon class="w-4 h-4" />
            <span>{{ formatDate(article.publishedAt) }}</span>
          </div>

          <!-- Journal -->
          <div v-if="article.journal" class="flex items-center space-x-1 rtl:space-x-reverse">
            <BookOpenIcon class="w-4 h-4" />
            <span class="line-clamp-1">{{ article.journal }}</span>
          </div>
        </div>

        <!-- Citation Count -->
        <div class="flex items-center space-x-1 rtl:space-x-reverse">
          <span>{{ article.citationCount }}</span>
          <span>استناد</span>
        </div>
      </div>

      <!-- Tags -->
      <div v-if="article.tags && article.tags.length > 0" class="flex flex-wrap gap-2">
        <span
          v-for="tag in article.tags.slice(0, 3)"
          :key="tag"
          class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
        >
          {{ tag }}
        </span>
        <span
          v-if="article.tags.length > 3"
          class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-xs rounded-full"
        >
          +{{ article.tags.length - 3 }}
        </span>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2 rtl:space-x-reverse">
          <!-- Save Button -->
          <button
            @click="toggleSave"
            :class="[
              'p-2 rounded-neu text-sm transition-all duration-200 focus-neu',
              article.isSaved
                ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                : 'text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400'
            ]"
            :title="article.isSaved ? 'حذف از ذخیره شده‌ها' : 'ذخیره مقاله'"
          >
            <BookmarkIcon
              :class="[
                'w-4 h-4',
                article.isSaved ? 'fill-current' : ''
              ]"
            />
          </button>

          <!-- Share Button -->
          <button
            @click="shareArticle"
            class="p-2 rounded-neu text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 focus-neu"
            title="اشتراک‌گذاری"
          >
            <ShareIcon class="w-4 h-4" />
          </button>

          <!-- Download Button -->
          <button
            v-if="article.downloadUrl"
            @click="downloadArticle"
            class="p-2 rounded-neu text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 focus-neu"
            title="دانلود PDF"
          >
            <ArrowDownTrayIcon class="w-4 h-4" />
          </button>
        </div>

        <!-- Read More Button -->
        <router-link
          :to="`/article/${article.id}`"
          class="btn-neu-primary text-sm px-4 py-2 focus-neu"
        >
          مطالعه بیشتر
        </router-link>
      </div>
    </div>
  </article>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  UserIcon,
  CalendarIcon,
  BookOpenIcon,
  BookmarkIcon,
  ShareIcon,
  ArrowDownTrayIcon,
} from '@heroicons/vue/24/outline'

// Props
interface Author {
  id: string
  name: string
}

interface Article {
  id: string
  title: string
  abstract: string
  authors: Author[]
  category: string
  publishedAt: string
  journal?: string
  citationCount: number
  tags?: string[]
  thumbnail?: string
  downloadUrl?: string
  isSaved: boolean
}

interface Props {
  article: Article
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  save: [articleId: string]
  unsave: [articleId: string]
  share: [article: Article]
  download: [article: Article]
}>()

// Methods
const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    'علوم کامپیوتر': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
    'مهندسی': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
    'پزشکی': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
    'فیزیک': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
    'شیمی': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
    'ریاضی': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300',
  }
  return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date)
}

const toggleSave = () => {
  if (props.article.isSaved) {
    emit('unsave', props.article.id)
  } else {
    emit('save', props.article.id)
  }
}

const shareArticle = () => {
  emit('share', props.article)
}

const downloadArticle = () => {
  emit('download', props.article)
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
